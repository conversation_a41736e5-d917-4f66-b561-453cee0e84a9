import Link from 'next/link'
import { <PERSON><PERSON><PERSON>, BookOpen, Users, TrendingUp } from 'lucide-react'

export default function Home() {
  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <section className="relative py-16 lg:py-24 bg-background">
        <div className="container mx-auto px-4">
          <div className="grid lg:grid-cols-2 gap-12 items-center">
            {/* Left Content */}
            <div className="space-y-8">
              <div className="space-y-6">
                <h1 className="text-4xl lg:text-6xl font-bold leading-tight">
                  Share Your{' '}
                  <span className="text-primary">Stories</span>{' '}
                  with the World
                </h1>
                <p className="text-lg text-muted-foreground max-w-lg">
                  A modern blogging platform where writers, creators, and thinkers come together to share ideas and inspire others.
                </p>
              </div>
              <div className="flex flex-col sm:flex-row gap-4">
                <Link
                  href="/auth/signup"
                  className="inline-flex items-center justify-center px-8 py-3 bg-primary text-primary-foreground rounded-lg hover:bg-primary/90 transition-colors font-medium"
                >
                  Start Writing
                  <ArrowRight className="ml-2 h-4 w-4" />
                </Link>
                <Link
                  href="/articles"
                  className="inline-flex items-center justify-center px-8 py-3 border border-input bg-background hover:bg-accent hover:text-accent-foreground rounded-lg transition-colors font-medium"
                >
                  Explore Articles
                </Link>
              </div>
            </div>

            {/* Right Content - Visual Element */}
            <div className="relative">
              <div className="relative w-full h-96 bg-gradient-to-br from-primary/10 to-secondary/10 rounded-2xl overflow-hidden">
                {/* Abstract illustration representing content creation */}
                <div className="absolute inset-0 flex items-center justify-center">
                  <div className="relative w-64 h-64">
                    {/* Central writing/content icon */}
                    <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-16 h-16 bg-primary rounded-lg flex items-center justify-center">
                      <BookOpen className="h-8 w-8 text-primary-foreground" />
                    </div>

                    {/* Floating content elements */}
                    <div className="absolute top-8 right-8 w-8 h-8 bg-green-500 rounded-full flex items-center justify-center">
                      <Users className="h-4 w-4 text-white" />
                    </div>
                    <div className="absolute bottom-8 left-8 w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center">
                      <TrendingUp className="h-4 w-4 text-white" />
                    </div>
                    <div className="absolute top-16 left-16 w-6 h-6 bg-yellow-500 rounded-full"></div>
                    <div className="absolute bottom-16 right-16 w-6 h-6 bg-purple-500 rounded-full"></div>

                    {/* Connecting lines representing connections */}
                    <div className="absolute top-1/2 left-1/2 w-32 h-0.5 bg-primary/30 transform -translate-x-1/2 -translate-y-1/2 rotate-45"></div>
                    <div className="absolute top-1/2 left-1/2 w-24 h-0.5 bg-primary/30 transform -translate-x-1/2 -translate-y-1/2 -rotate-45"></div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-20 bg-muted/30">
        <div className="container mx-auto px-4">
          <div className="mb-16">
            <div className="inline-flex items-center px-3 py-1 rounded-full bg-primary/10 text-primary text-sm font-medium mb-4">
              Features
            </div>
            <h2 className="text-3xl md:text-4xl font-bold mb-4 max-w-2xl">
              Everything You Need to Create Amazing Content
            </h2>
            <p className="text-lg text-muted-foreground max-w-2xl">
              Powerful tools and features designed to help you write, publish, and grow your audience with professional-grade content management.
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
            {/* Rich Editor Feature */}
            <div className="bg-background rounded-xl p-6 shadow-sm border border-border/50">
              <div className="flex items-center gap-3 mb-4">
                <div className="w-10 h-10 bg-primary rounded-lg flex items-center justify-center">
                  <BookOpen className="h-5 w-5 text-primary-foreground" />
                </div>
                <h3 className="text-lg font-semibold">Rich Editor</h3>
              </div>
              <p className="text-muted-foreground mb-4">
                Write with our intuitive rich text editor that supports markdown, images, and advanced formatting options.
              </p>
              <div className="bg-muted/50 rounded-lg p-4 h-24 flex items-center justify-center">
                <div className="text-center">
                  <BookOpen className="h-6 w-6 text-primary mx-auto mb-1" />
                  <p className="text-xs text-muted-foreground">Editor Preview</p>
                </div>
              </div>
            </div>

            {/* Community Feature */}
            <div className="bg-background rounded-xl p-6 shadow-sm border border-border/50">
              <div className="flex items-center gap-3 mb-4">
                <div className="w-10 h-10 bg-green-500 rounded-lg flex items-center justify-center">
                  <Users className="h-5 w-5 text-white" />
                </div>
                <h3 className="text-lg font-semibold">Community</h3>
              </div>
              <p className="text-muted-foreground mb-4">
                Connect with other writers, get feedback, and build your audience through meaningful engagement.
              </p>
              <div className="bg-muted/50 rounded-lg p-4 h-24 flex items-center justify-center">
                <div className="text-center">
                  <Users className="h-6 w-6 text-green-500 mx-auto mb-1" />
                  <p className="text-xs text-muted-foreground">Community Hub</p>
                </div>
              </div>
            </div>

            {/* Analytics Feature */}
            <div className="bg-background rounded-xl p-6 shadow-sm border border-border/50">
              <div className="flex items-center gap-3 mb-4">
                <div className="w-10 h-10 bg-blue-500 rounded-lg flex items-center justify-center">
                  <TrendingUp className="h-5 w-5 text-white" />
                </div>
                <h3 className="text-lg font-semibold">Analytics</h3>
              </div>
              <p className="text-muted-foreground mb-4">
                Track your article performance with detailed analytics, insights, and growth metrics.
              </p>
              <div className="bg-muted/50 rounded-lg p-4 h-24 flex items-center justify-center">
                <div className="text-center">
                  <TrendingUp className="h-6 w-6 text-blue-500 mx-auto mb-1" />
                  <p className="text-xs text-muted-foreground">Analytics Dashboard</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Stats Section */}
      <section className="py-20 bg-background">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <div className="inline-flex items-center px-3 py-1 rounded-full bg-primary/10 text-primary text-sm font-medium mb-4">
              Trusted by creators
            </div>
            <h2 className="text-2xl md:text-3xl font-bold mb-4">
              Join a thriving community of writers
            </h2>
          </div>
          <div className="grid md:grid-cols-3 gap-8">
            <div className="text-center p-6 rounded-xl bg-muted/30">
              <div className="text-4xl font-bold text-primary mb-2">10K+</div>
              <div className="text-muted-foreground font-medium">Articles Published</div>
              <p className="text-sm text-muted-foreground mt-2">Stories shared and read by millions</p>
            </div>
            <div className="text-center p-6 rounded-xl bg-muted/30">
              <div className="text-4xl font-bold text-primary mb-2">5K+</div>
              <div className="text-muted-foreground font-medium">Active Writers</div>
              <p className="text-sm text-muted-foreground mt-2">Creative minds building their audience</p>
            </div>
            <div className="text-center p-6 rounded-xl bg-muted/30">
              <div className="text-4xl font-bold text-primary mb-2">50K+</div>
              <div className="text-muted-foreground font-medium">Monthly Readers</div>
              <p className="text-sm text-muted-foreground mt-2">Engaged audience discovering content</p>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-muted/30">
        <div className="container mx-auto px-4">
          <div className="grid lg:grid-cols-2 gap-12 items-center">
            {/* Left Content */}
            <div className="space-y-6">
              <h2 className="text-3xl md:text-4xl font-bold">
                Ready to Start Your Writing Journey?
              </h2>
              <p className="text-lg text-muted-foreground">
                Join thousands of writers who are already sharing their stories and building their audience with our powerful blogging platform.
              </p>
              <div className="flex flex-col sm:flex-row gap-4">
                <Link
                  href="/auth/signup"
                  className="inline-flex items-center justify-center px-8 py-3 bg-primary text-primary-foreground rounded-lg hover:bg-primary/90 transition-colors font-medium"
                >
                  Get Started for Free
                  <ArrowRight className="ml-2 h-4 w-4" />
                </Link>
                <Link
                  href="/articles"
                  className="inline-flex items-center justify-center px-8 py-3 border border-input bg-background hover:bg-accent hover:text-accent-foreground rounded-lg transition-colors font-medium"
                >
                  Browse Articles
                </Link>
              </div>
            </div>

            {/* Right Content - Visual Element */}
            <div className="relative">
              <div className="relative w-full h-64 bg-gradient-to-br from-primary/10 to-secondary/10 rounded-2xl overflow-hidden">
                <div className="absolute inset-0 flex items-center justify-center">
                  <div className="relative w-48 h-48">
                    {/* Central elements representing success */}
                    <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-12 h-12 bg-primary rounded-full flex items-center justify-center">
                      <ArrowRight className="h-6 w-6 text-primary-foreground" />
                    </div>
                    <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-24 h-24 border-2 border-primary/30 rounded-full"></div>

                    {/* Success indicators */}
                    <div className="absolute top-4 right-4 w-6 h-6 bg-green-500 rounded-full"></div>
                    <div className="absolute bottom-4 left-4 w-4 h-4 bg-blue-500 rounded-full"></div>
                    <div className="absolute top-8 left-8 w-3 h-3 bg-yellow-500 rounded-full"></div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
  )
}
