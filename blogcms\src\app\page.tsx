import Link from 'next/link'
import { ArrowRight, Search, BarChart3, Mail, MousePointer, Zap, Target, Play } from 'lucide-react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'

export default function Home() {
  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <section className="relative py-16 lg:py-24 bg-background">
        <div className="container mx-auto px-4">
          <div className="grid lg:grid-cols-2 gap-12 items-center">
            {/* Left Content */}
            <div className="space-y-8">
              <div className="space-y-6">
                <h1 className="text-4xl lg:text-6xl font-bold leading-tight">
                  Navigating the{' '}
                  <span className="text-primary">digital landscape</span>{' '}
                  for success
                </h1>
                <p className="text-lg text-muted-foreground max-w-lg">
                  Our digital marketing agency helps businesses grow and succeed online through a range of services including SEO, PPC, social media, and content marketing.
                </p>
              </div>
              <Button size="lg" className="bg-foreground text-background hover:bg-foreground/90">
                Book a consultation
              </Button>
            </div>

            {/* Right Content - Abstract Illustration */}
            <div className="relative">
              <div className="relative w-full h-96 bg-gradient-to-br from-primary/10 to-secondary/10 rounded-2xl overflow-hidden">
                {/* Abstract geometric shapes */}
                <div className="absolute inset-0 flex items-center justify-center">
                  <div className="relative w-64 h-64">
                    {/* Central circle */}
                    <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-16 h-16 bg-primary rounded-full"></div>

                    {/* Orbiting elements */}
                    <div className="absolute top-8 right-8 w-8 h-8 bg-green-500 rounded-full"></div>
                    <div className="absolute bottom-8 left-8 w-6 h-6 bg-yellow-500 rounded-full"></div>
                    <div className="absolute top-16 left-16 w-4 h-4 bg-blue-500 rounded-full"></div>
                    <div className="absolute bottom-16 right-16 w-5 h-5 bg-purple-500 rounded-full"></div>

                    {/* Connecting lines */}
                    <div className="absolute top-1/2 left-1/2 w-32 h-0.5 bg-primary/30 transform -translate-x-1/2 -translate-y-1/2 rotate-45"></div>
                    <div className="absolute top-1/2 left-1/2 w-24 h-0.5 bg-primary/30 transform -translate-x-1/2 -translate-y-1/2 -rotate-45"></div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Trusted By Section */}
      <section className="py-12 bg-muted/30">
        <div className="container mx-auto px-4">
          <div className="text-center mb-8">
            <p className="text-sm text-muted-foreground uppercase tracking-wider">Trusted by leading companies</p>
          </div>
          <div className="flex flex-wrap justify-center items-center gap-8 opacity-60">
            <div className="text-2xl font-bold">amazon</div>
            <div className="text-2xl font-bold">dribbble</div>
            <div className="text-2xl font-bold">HubSpot</div>
            <div className="text-2xl font-bold">Notion</div>
            <div className="text-2xl font-bold">NETFLIX</div>
            <div className="text-2xl font-bold">zoom</div>
          </div>
        </div>
      </section>

      {/* Services Section */}
      <section className="py-20 bg-background">
        <div className="container mx-auto px-4">
          <div className="mb-16">
            <Badge variant="success" className="mb-4">Services</Badge>
            <h2 className="text-3xl md:text-4xl font-bold mb-4 max-w-2xl">
              At our digital marketing agency, we offer a range of services to help businesses grow and succeed online.
            </h2>
          </div>

          <div className="grid md:grid-cols-2 gap-6">
            {/* Search Engine Optimization */}
            <Card className="bg-muted/30 border-0">
              <CardHeader className="pb-4">
                <div className="flex items-center gap-3 mb-4">
                  <div className="w-8 h-8 bg-foreground rounded-full flex items-center justify-center">
                    <Search className="h-4 w-4 text-background" />
                  </div>
                  <CardTitle className="text-lg">Search engine optimization</CardTitle>
                </div>
                <div className="bg-muted/50 rounded-lg p-4 h-32 flex items-center justify-center">
                  <div className="text-center">
                    <div className="w-16 h-16 bg-primary/20 rounded-lg mx-auto mb-2 flex items-center justify-center">
                      <Search className="h-8 w-8 text-primary" />
                    </div>
                    <p className="text-sm text-muted-foreground">SEO Analytics</p>
                  </div>
                </div>
              </CardHeader>
            </Card>

            {/* Pay-per-click Advertising */}
            <Card className="bg-foreground text-background border-0">
              <CardHeader className="pb-4">
                <div className="flex items-center gap-3 mb-4">
                  <div className="w-8 h-8 bg-background rounded-full flex items-center justify-center">
                    <MousePointer className="h-4 w-4 text-foreground" />
                  </div>
                  <CardTitle className="text-lg text-background">Pay-per-click advertising</CardTitle>
                </div>
                <div className="bg-background/10 rounded-lg p-4 h-32 flex items-center justify-center">
                  <div className="text-center">
                    <div className="w-16 h-16 bg-background/20 rounded-lg mx-auto mb-2 flex items-center justify-center">
                      <MousePointer className="h-8 w-8 text-background" />
                    </div>
                    <p className="text-sm text-background/70">PPC Campaign</p>
                  </div>
                </div>
              </CardHeader>
            </Card>

            {/* Social Media Marketing */}
            <Card className="bg-foreground text-background border-0">
              <CardHeader className="pb-4">
                <div className="flex items-center gap-3 mb-4">
                  <div className="w-8 h-8 bg-background rounded-full flex items-center justify-center">
                    <Zap className="h-4 w-4 text-foreground" />
                  </div>
                  <CardTitle className="text-lg text-background">Social media marketing</CardTitle>
                </div>
                <div className="bg-background/10 rounded-lg p-4 h-32 flex items-center justify-center">
                  <div className="text-center">
                    <div className="w-16 h-16 bg-background/20 rounded-lg mx-auto mb-2 flex items-center justify-center">
                      <Zap className="h-8 w-8 text-background" />
                    </div>
                    <p className="text-sm text-background/70">Social Analytics</p>
                  </div>
                </div>
              </CardHeader>
            </Card>

            {/* Email Marketing */}
            <Card className="bg-muted/30 border-0">
              <CardHeader className="pb-4">
                <div className="flex items-center gap-3 mb-4">
                  <div className="w-8 h-8 bg-foreground rounded-full flex items-center justify-center">
                    <Mail className="h-4 w-4 text-background" />
                  </div>
                  <CardTitle className="text-lg">Email marketing</CardTitle>
                </div>
                <div className="bg-muted/50 rounded-lg p-4 h-32 flex items-center justify-center">
                  <div className="text-center">
                    <div className="w-16 h-16 bg-primary/20 rounded-lg mx-auto mb-2 flex items-center justify-center">
                      <Mail className="h-8 w-8 text-primary" />
                    </div>
                    <p className="text-sm text-muted-foreground">Email Campaign</p>
                  </div>
                </div>
              </CardHeader>
            </Card>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-muted/30">
        <div className="container mx-auto px-4">
          <div className="grid lg:grid-cols-2 gap-12 items-center">
            {/* Left Content */}
            <div className="space-y-6">
              <h2 className="text-3xl md:text-4xl font-bold">
                Let's make things happen
              </h2>
              <p className="text-lg text-muted-foreground">
                Contact us today to learn more about how our digital marketing services can help your business grow and succeed online.
              </p>
              <Button size="lg" className="bg-foreground text-background hover:bg-foreground/90">
                Get your free proposal
              </Button>
            </div>

            {/* Right Content - Abstract Illustration */}
            <div className="relative">
              <div className="relative w-full h-64 bg-gradient-to-br from-primary/10 to-secondary/10 rounded-2xl overflow-hidden">
                <div className="absolute inset-0 flex items-center justify-center">
                  <div className="relative w-48 h-48">
                    {/* Central elements */}
                    <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-12 h-12 bg-primary rounded-full"></div>
                    <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-24 h-24 border-2 border-primary/30 rounded-full"></div>

                    {/* Floating elements */}
                    <div className="absolute top-4 right-4 w-6 h-6 bg-green-500 rounded-full"></div>
                    <div className="absolute bottom-4 left-4 w-4 h-4 bg-yellow-500 rounded-full"></div>
                    <div className="absolute top-8 left-8 w-3 h-3 bg-blue-500 rounded-full"></div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Case Study Section */}
      <section className="py-20 bg-background">
        <div className="container mx-auto px-4">
          <div className="mb-16">
            <Badge variant="success" className="mb-4">Case study</Badge>
            <h2 className="text-3xl md:text-4xl font-bold mb-4 max-w-2xl">
              Explore Real-Life Examples of Our Proven Digital Marketing Success through Our Case Studies
            </h2>
          </div>

          <div className="grid md:grid-cols-3 gap-6">
            {/* Case Study 1 */}
            <Card className="bg-foreground text-background border-0">
              <CardContent className="p-6">
                <p className="text-sm text-background/70 mb-4">
                  For a local restaurant, we implemented a targeted PPC campaign that resulted in a 50% increase in website traffic and a 25% increase in sales.
                </p>
                <Button variant="link" className="text-background hover:text-background/80 p-0">
                  Learn more <ArrowRight className="ml-1 h-3 w-3" />
                </Button>
              </CardContent>
            </Card>

            {/* Case Study 2 */}
            <Card className="bg-foreground text-background border-0">
              <CardContent className="p-6">
                <p className="text-sm text-background/70 mb-4">
                  For a B2B software company, we developed an SEO strategy that resulted in a first page ranking for key keywords and a 200% increase in organic traffic.
                </p>
                <Button variant="link" className="text-background hover:text-background/80 p-0">
                  Learn more <ArrowRight className="ml-1 h-3 w-3" />
                </Button>
              </CardContent>
            </Card>

            {/* Case Study 3 */}
            <Card className="bg-foreground text-background border-0">
              <CardContent className="p-6">
                <p className="text-sm text-background/70 mb-4">
                  For a national retail chain, we created a social media marketing campaign that increased followers by 25% and generated a 20% increase in online sales.
                </p>
                <Button variant="link" className="text-background hover:text-background/80 p-0">
                  Learn more <ArrowRight className="ml-1 h-3 w-3" />
                </Button>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>
    </div>
  )
}
