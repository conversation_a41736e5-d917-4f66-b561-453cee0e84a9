# BlogCMS - Modern Blogging Platform

A comprehensive blogging platform built with Next.js 15, featuring a powerful rich text editor, user management, and analytics. Perfect for writers, creators, and publishers who want a modern, feature-rich content management system.

## 🚀 Features

### 📝 Advanced Rich Text Editor
- **Complete formatting toolkit**: Bold, italic, underline, strikethrough, headings, lists
- **Media support**: Images, videos (YouTube/Vimeo), GIFs, PDFs
- **Advanced features**: Tables, charts, code blocks, task lists
- **Text styling**: Colors, highlights, fonts, subscript/superscript
- **Alignment options**: Left, center, right, justify
- **Drag & drop**: File uploads with preview
- **Auto-save**: Never lose your work

### 👥 User Management
- **Multi-role system**: Admin, Editor, Author, Reader
- **Authentication**: Secure JWT-based auth with NextAuth.js
- **User profiles**: Bio, avatar, social links
- **Follow system**: Follow your favorite authors

### 📊 Content Management
- **Article workflow**: Drafts, review, publishing, archiving
- **Categories & tags**: Organize content effectively
- **SEO optimization**: Meta descriptions, Open Graph tags
- **Featured images**: Eye-catching article headers
- **Reading time**: Automatic calculation
- **View tracking**: Analytics for engagement

### 🎨 Modern UI/UX
- **Responsive design**: Works on all devices
- **Clean interface**: Intuitive and user-friendly
- **Dark/light themes**: Customizable appearance
- **Loading states**: Smooth user experience
- **Error handling**: Graceful error management

### 📈 Analytics & Insights
- **Dashboard**: Comprehensive author analytics
- **View counts**: Track article performance
- **Engagement metrics**: Comments, reactions, bookmarks
- **User statistics**: Follower counts, article metrics

## 🛠 Tech Stack

- **Frontend**: Next.js 15, React 19, TypeScript
- **Styling**: Tailwind CSS, Radix UI components
- **Editor**: TipTap with extensive extensions
- **Authentication**: NextAuth.js with JWT
- **Database**: Prisma ORM with SQLite (dev) / PostgreSQL (prod)
- **Charts**: Recharts for data visualization
- **File handling**: React Dropzone
- **Icons**: Lucide React

## 🚀 Quick Start

### Prerequisites
- Node.js 18+
- npm or yarn

### Installation

1. **Install dependencies**
   ```bash
   npm install
   ```

2. **Set up the database**
   ```bash
   npm run db:generate
   npm run db:push
   npm run db:seed
   ```

3. **Start the development server**
   ```bash
   npm run dev
   ```

4. **Open your browser**
   Navigate to `http://localhost:3000`

### Demo Accounts
- **Admin**: <EMAIL> / admin123
- **Author**: <EMAIL> / author123

## 🎯 Key Features Implemented

### Rich Text Editor
- ✅ Text formatting (bold, italic, underline, etc.)
- ✅ Headings (H1, H2, H3)
- ✅ Lists (bullet, numbered, task lists)
- ✅ Tables with customizable rows/columns
- ✅ Images with drag & drop upload
- ✅ Videos (YouTube/Vimeo embedding)
- ✅ Charts (bar, line, pie charts)
- ✅ Code blocks with syntax highlighting
- ✅ Text alignment and colors
- ✅ Links and media insertion
- ✅ Undo/redo functionality

### User System
- ✅ User registration and authentication
- ✅ Role-based access control
- ✅ User profiles and settings
- ✅ Password security

### Content Management
- ✅ Article creation and editing
- ✅ Draft and publish workflow
- ✅ Categories and tags
- ✅ SEO metadata
- ✅ Featured images

### Dashboard & Analytics
- ✅ Author dashboard
- ✅ Article statistics
- ✅ View tracking
- ✅ Content management

## 🔧 Available Scripts

- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run start` - Start production server
- `npm run lint` - Run ESLint
- `npm run db:generate` - Generate Prisma client
- `npm run db:push` - Push schema to database
- `npm run db:seed` - Seed database with sample data
- `npm run db:studio` - Open Prisma Studio

## 🌟 What Makes This Special

1. **Production-Ready**: Built with enterprise-grade technologies and best practices
2. **Comprehensive Editor**: One of the most feature-rich editors available
3. **Scalable Architecture**: Modular design that grows with your needs
4. **Modern Stack**: Latest versions of Next.js, React, and TypeScript
5. **Developer Experience**: Excellent tooling and documentation
