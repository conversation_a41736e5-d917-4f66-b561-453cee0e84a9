AI-Based Development Protocol: Code Discipline + Task Management
📜 OVERARCHING PRINCIPLES
✅ This document outlines strict development practices to be followed by the AI system when participating in any code creation, review, or enhancement tasks.

🚫 NON-NEGOTIABLE GUARDRAILS
1. DO NOT overwrite, delete, or modify any existing stable code or styles unless explicitly authorized.
2. DO NOT create speculative or random files, features, or APIs outside the project scope.
3. DO NOT break or diverge from the existing design system or code structure.
4. DO NOT merge or commit without verification of non-breaking changes and sync with main.

✅ MANDATORY RULES
1. Maintain a Clean, Organized Root
   a. Keep root-level files minimal (README.md, .env, package.json, etc.)
   b. No unused folders, dummy files, or temp directories.
   c. Delete exact duplicates or replicated files after confirming their obsolescence.

2. PDP File (Project Development Protocol)

   a. Always update PDP.md with:
      i. Project version updates
      ii. Directory structure map
      iii. Summary of files changed or added
      iv. Reason for each structural/code update
      v. Any design tokens, fonts, or assets added/changed
      vi. Current status and progress

   b. This is your central doc for project auditing.

3. TaskList Management (tasks.md)
   a. Log every task or feature being worked on.

   b. Include:
      i. Task name + date
      ii. Dependencies (if any)
      iii. Estimated time
      iv. Priority (High, Medium, Low)
      v. Status ☐ To Do | ☐ In Progress | ☑ Done
      vi. Refactor tags, blockers, or design feedback
      vii. Relevant links, PRs, or issues
      viii. Any blockers or dependencies
      

   c. Structure your workflow following Extreme Programming (XP):
      i. Small iterations
      ii. Continuous feedback
      iii. Collective code ownership
      iv. Simple design + refactor frequently

4. Code Quality and Architecture
   a. Apply modular development:
      i. Isolate UI components, business logic, and data layers
      ii. Use reusable functions/hooks/components
      iii. Follow DRY (Don't Repeat Yourself) principle
      iv. Avoid deep nesting of components

   b. Perform a full code review before introducing any new module:
      i. Eliminate duplicates
      ii. Ensure consistency with existing architecture
      iii. Respect file hierarchies (components, pages, utils, services)
      iv. Verify no breaking changes
      v. Confirm all tests pass
      vi. Merge redundant logic
      
   c. Use version control for all updates:
      i. Separate branches for each feature/fix
      ii. Descriptive commit messages
      iii. Use feature flags or toggles where needed
      iv. Resolve conflicts manually
      v. Verify no breaking changes
      vi. Automated testing on all branches
      vii. Semantic versioning for releases

5. Refactor Safely
   a. Mark old code as deprecated but do not delete unless tested.
   b. Extract logic from cluttered files into dedicated services/helpers.
   c. Maintain naming conventions across all folders and modules.
   d. Update PDP.md with any structural changes.
   e. Update tasks.md with completed task.

6. Design Consistency
   a. Follow atomic or design-system components strictly.
   b. Replicate exact typography, layout, spacing, and behavior unless change is requested.
   c. Document every UI update in the PDP.md.

🔧 DEVELOPMENT INSTRUCTIONS FOR AI
When writing or editing code:
   a. First: Review the entire codebase structure.
   b. Identify: Any duplicates or disorder in file organization.
   c. Next:
      i. Suggest a directory cleanup plan (non-destructive).
      ii. Generate a safe refactor map.
      iii. Apply changes only in isolated branches or with feature flags.
      iv. Verify no breaking changes.
   d. Final step: Update PDP.md and tasks.md accordingly.

📁 FILE STRUCTURE CHECKLIST
Task	Requirement
✅	Clean root folder (no unreferenced files)
✅	All code folders follow camelCase or kebab-case naming
✅	All components, hooks, and services are modular and reusable
✅	Static assets organized by type (/assets/images, /assets/svg, etc.)
✅	Each route/page has its corresponding component

✅ SAMPLE tasks.md FORMAT

# 📋 Task List (Extreme Programming - AI-Driven Dev)

- [x] ✅ Audit existing file structure for duplicates (2025-06-02)
- [ ] ☐ Modularize navigation and footer components (in progress)
- [ ] ☐ Refactor `/utils/api.js` to isolate fetch logic
- [ ] ☐ Update PDP with folder restructure changes
- [ ] ☐ Add feature flag for experimental sidebar

✅ SAMPLE PDP.md UPDATE ENTRY
## 🔄 Project Update – 2025-06-02

### ✅ What Changed:
- Removed duplicated `/components/Header.js` and `/components/header.jsx`
- Consolidated into `/components/Header/index.jsx`
- Moved unused `/test/` folder to `/archive/`

### 🗂️ Current Directory Structure (simplified)
- /src
  - /components
  - /pages
  - /services
  - /styles
  - /utils
- /public
- /archive

### 📌 Notes
- Design tokens untouched.
- No regressions found.
- Next: Modularize layout logic.