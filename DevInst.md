AI-Based Development Protocol: Code Discipline + Task Management
📜 OVERARCHING PRINCIPLES
✅ This document outlines strict development practices to be followed by the AI system when participating in any code creation, review, or enhancement tasks.

🚫 NON-NEGOTIABLE GUARDRAILS
1. DO NOT overwrite, delete, or modify any existing stable code or styles unless explicitly authorized.
2. DO NOT create speculative or random files, features, or APIs outside the project scope.
3. DO NOT break or diverge from the existing design system or code structure.
4. DO NOT merge or commit without verification of non-breaking changes and sync with main.
5. DO NOT install dependencies without explicit user permission and package manager usage.
6. DO NOT deploy code or make production changes without explicit authorization.
7. DO NOT modify database schemas or migrations without proper backup and rollback plans.
8. DO NOT bypass security measures, authentication, or authorization checks.
9. DO NOT create duplicate functionality that already exists in the codebase.

✅ MANDATORY RULES
1. Maintain a Clean, Organized Root
   a. Keep root-level files minimal (README.md, .env, package.json, etc.)
   b. No unused folders, dummy files, or temp directories.
   c. Delete exact duplicates or replicated files after confirming their obsolescence.

2. PDP File (Project Development Protocol)

   a. Always update PDP.md with:
      i. Project version updates
      ii. Directory structure map
      iii. Summary of files changed or added
      iv. Reason for each structural/code update
      v. Any design tokens, fonts, or assets added/changed
      vi. Current status and progress

   b. This is your central doc for project auditing.

3. TaskList Management (tasks.md)
   a. Log every task or feature being worked on.

   b. Include:
      i. Task name + date
      ii. Dependencies (if any)
      iii. Estimated time
      iv. Priority (High, Medium, Low)
      v. Status ☐ To Do | ☐ In Progress | ☑ Done
      vi. Refactor tags, blockers, or design feedback
      vii. Relevant links, PRs, or issues
      viii. Any blockers or dependencies
      ix. Specific instructions for code, testing or verification

   c. Structure your workflow following Extreme Programming (XP):
      i. Small iterations
      ii. Continuous feedback
      iii. Collective code ownership
      iv. Simple design + refactor frequently

4. Code Quality and Architecture
   a. Apply modular development:
      i. Isolate UI components, business logic, and data layers
      ii. Use reusable functions/hooks/components
      iii. Follow DRY (Don't Repeat Yourself) principle
      iv. Avoid deep nesting of components
      v. Implement proper TypeScript types and interfaces
      vi. Follow SOLID principles for maintainable code

   b. Perform a full code review before introducing any new module:
      i. Eliminate duplicates
      ii. Ensure consistency with existing architecture
      iii. Respect file hierarchies (components, pages, utils, services)
      iv. Verify no breaking changes
      v. Confirm all tests pass
      vi. Merge redundant logic
      vii. Check for security vulnerabilities
      viii. Validate performance implications

   c. Use version control for all updates:
      i. Separate branches for each feature/fix
      ii. Descriptive commit messages following conventional commits
      iii. Use feature flags or toggles where needed
      iv. Resolve conflicts manually
      v. Verify no breaking changes
      vi. Automated testing on all branches
      vii. Semantic versioning for releases
      viii. Code review approval required before merging

   d. Testing Requirements:
      i. Write tests BEFORE implementing features (TDD)
      ii. Maintain minimum 80% code coverage
      iii. Include unit, integration, and e2e tests
      iv. Test error handling and edge cases
      v. Performance tests for critical paths
      vi. Security tests for authentication/authorization

5. Refactor Safely
   a. Mark old code as deprecated but do not delete unless tested.
   b. Extract logic from cluttered files into dedicated services/helpers.
   c. Maintain naming conventions across all folders and modules.
   d. Update PDP.md with any structural changes.
   e. Update tasks.md with completed task.

6. Design Consistency
   a. Follow atomic or design-system components strictly.
   b. Replicate exact typography, layout, spacing, and behavior unless change is requested.
   c. Document every UI update in the PDP.md.
   d. Ensure responsive design across all devices.
   e. Maintain accessibility standards (WCAG 2.1 AA).
   f. Optimize images and assets for performance.

7. Security and Performance
   a. Implement proper input validation and sanitization.
   b. Use parameterized queries to prevent SQL injection.
   c. Implement rate limiting on API endpoints.
   d. Ensure proper authentication and authorization.
   e. Monitor and optimize database queries.
   f. Implement proper error handling without exposing sensitive data.
   g. Use HTTPS and secure headers.
   h. Regular security audits and dependency updates.

8. Documentation and Communication
   a. Maintain comprehensive API documentation.
   b. Document all architectural decisions.
   c. Keep README files updated with setup instructions.
   d. Comment complex business logic clearly.
   e. Update user-facing documentation for new features.
   f. Maintain changelog for version releases.

🔧 DEVELOPMENT INSTRUCTIONS FOR AI
When writing or editing code:
   a. First: Review the entire codebase structure using codebase-retrieval tool.
   b. Identify: Any duplicates or disorder in file organization.
   c. Plan: Create detailed implementation plan before making changes.
   d. Next:
      i. Suggest a directory cleanup plan (non-destructive).
      ii. Generate a safe refactor map.
      iii. Apply changes only in isolated branches or with feature flags.
      iv. Verify no breaking changes.
      v. Run all tests to ensure functionality.
      vi. Check for security vulnerabilities.
      vii. Validate performance impact.
   e. Package Management: Always use package managers (npm, yarn, pip, etc.) instead of manually editing package files.
   f. Testing: Write tests before implementing features (TDD approach).
   g. Final step: Update PDP.md and tasks.md accordingly.

🛡️ SECURITY CHECKLIST
Before deploying any code:
   ✅ Input validation implemented
   ✅ Authentication/authorization verified
   ✅ SQL injection prevention confirmed
   ✅ XSS protection in place
   ✅ CSRF protection enabled
   ✅ Rate limiting configured
   ✅ Error handling doesn't expose sensitive data
   ✅ Dependencies updated and scanned for vulnerabilities

📁 FILE STRUCTURE CHECKLIST
Task	Requirement
✅	Clean root folder (no unreferenced files)
✅	All code folders follow camelCase or kebab-case naming
✅	All components, hooks, and services are modular and reusable
✅	Static assets organized by type (/assets/images, /assets/svg, etc.)
✅	Each route/page has its corresponding component
✅	TypeScript types and interfaces properly defined
✅	Test files co-located with source files
✅	Environment variables properly configured
✅	Database migrations and seeds organized
✅	API documentation up to date

🚀 PERFORMANCE CHECKLIST
✅	Images optimized and compressed
✅	Lazy loading implemented where appropriate
✅	Database queries optimized with proper indexing
✅	Caching strategy implemented
✅	Bundle size optimized
✅	API response times under 500ms
✅	Page load times under 3 seconds
✅	Memory leaks checked and resolved

✅ SAMPLE tasks.md FORMAT

# 📋 Task List (Extreme Programming - AI-Driven Dev)

## 🔥 High Priority
- [x] ✅ Audit existing file structure for duplicates (2025-06-02) - 2h
- [ ] ☐ Implement user authentication system (in progress) - 8h
- [ ] ☐ Set up database schema and migrations - 4h

## 🟡 Medium Priority
- [ ] ☐ Modularize navigation and footer components - 3h
- [ ] ☐ Refactor `/utils/api.js` to isolate fetch logic - 2h
- [ ] ☐ Add comprehensive error handling - 4h

## 🟢 Low Priority
- [ ] ☐ Update PDP with folder restructure changes - 1h
- [ ] ☐ Add feature flag for experimental sidebar - 2h
- [ ] ☐ Optimize image loading performance - 3h

## 🧪 Testing Tasks
- [ ] ☐ Write unit tests for authentication module - 4h
- [ ] ☐ Set up e2e testing framework - 6h
- [ ] ☐ Add integration tests for API endpoints - 5h

✅ SAMPLE PDP.md UPDATE ENTRY
## 🔄 Project Update – 2025-06-02

### ✅ What Changed:
- Removed duplicated `/components/Header.js` and `/components/header.jsx`
- Consolidated into `/components/Header/index.jsx`
- Moved unused `/test/` folder to `/archive/`

### 🗂️ Current Directory Structure (simplified)
- /src
  - /components
  - /pages
  - /services
  - /styles
  - /utils
- /public
- /archive

### 📌 Notes
- Design tokens untouched.
- No regressions found.
- All tests passing.
- Security scan completed.
- Performance benchmarks maintained.
- Next: Modularize layout logic.

## 📊 MONITORING AND MAINTENANCE
### Daily Checks
- [ ] Review error logs and fix critical issues
- [ ] Monitor application performance metrics
- [ ] Check security alerts and dependency updates
- [ ] Verify backup systems are functioning

### Weekly Reviews
- [ ] Code quality metrics review
- [ ] Test coverage analysis
- [ ] Performance optimization opportunities
- [ ] Security vulnerability assessment
- [ ] Documentation updates needed

### Monthly Audits
- [ ] Full security audit
- [ ] Performance benchmarking
- [ ] Dependency updates and cleanup
- [ ] Architecture review and improvements
- [ ] User feedback analysis and prioritization

## 🎯 DEFINITION OF DONE
A task is considered complete when:
✅ Code is written and reviewed
✅ Tests are written and passing (unit, integration, e2e)
✅ Security checks completed
✅ Performance validated
✅ Documentation updated
✅ PDP.md and tasks.md updated
✅ No breaking changes introduced
✅ Accessibility standards met
✅ Cross-browser compatibility verified
✅ Mobile responsiveness confirmed